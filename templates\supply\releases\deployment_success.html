<!-- Success message for HTMX response -->
<div class="bg-green-50 border border-green-200 rounded-xl p-6">
    <div class="flex items-center">
        <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <div class="ml-4">
            <h3 class="text-lg font-semibold text-green-800">Release Deployed Successfully!</h3>
            <p class="text-green-700 mt-1">
                {{ release.name }} v{{ release.version }} has been deployed successfully at {{ release.actual_release_date|date:"M d, Y g:i A" }}.
            </p>
            {% if deployment_notes %}
                <div class="mt-3 p-3 bg-white border border-green-200 rounded-lg">
                    <p class="text-sm text-gray-700"><strong>Deployment Notes:</strong></p>
                    <p class="text-sm text-gray-700 mt-1 whitespace-pre-wrap">{{ deployment_notes }}</p>
                </div>
            {% endif %}
            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="ml-3 text-sm text-blue-700">
                        The release is now live. Monitor the system for any issues and be prepared to execute the rollback plan if necessary.
                    </p>
                </div>
            </div>
            <div class="mt-4 flex space-x-3">
                <a href="{% url 'supply:release_detail' release.id %}" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    View Release
                </a>
                <a href="{% url 'supply:releases_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-redirect after 5 seconds (longer for deployment success)
setTimeout(() => {
    window.location.href = "{% url 'supply:release_detail' release.id %}";
}, 5000);
</script>
