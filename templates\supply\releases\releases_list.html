<!-- Releases List Partial Template for HTMX Updates -->
<div id="releases-list" class="space-y-6">
    {% if page_obj %}
        <!-- Sort Options -->
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
                Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} release{{ page_obj.paginator.count|pluralize }}
            </div>
            <div class="flex items-center space-x-2">
                <label class="text-sm text-gray-600">Sort by:</label>
                <select hx-get="{% url 'supply:releases_dashboard' %}" 
                        hx-target="#releases-list" 
                        hx-include="[name='status'], [name='release_type'], [name='priority'], [name='search']"
                        name="sort"
                        class="text-sm border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="-planned_release_date" {% if sort_by == '-planned_release_date' %}selected{% endif %}>Release Date (Newest)</option>
                    <option value="planned_release_date" {% if sort_by == 'planned_release_date' %}selected{% endif %}>Release Date (Oldest)</option>
                    <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>Created (Newest)</option>
                    <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>Created (Oldest)</option>
                    <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name (A-Z)</option>
                    <option value="-name" {% if sort_by == '-name' %}selected{% endif %}>Name (Z-A)</option>
                    <option value="priority" {% if sort_by == 'priority' %}selected{% endif %}>Priority</option>
                    <option value="status" {% if sort_by == 'status' %}selected{% endif %}>Status</option>
                </select>
            </div>
        </div>

        <!-- Releases Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for release in page_obj %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-200">
                    <!-- Release Header -->
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">
                                    <a href="{% url 'supply:release_detail' release.id %}" 
                                       class="hover:text-blue-600 transition-colors duration-200">
                                        {{ release.name }}
                                    </a>
                                </h3>
                                <div class="flex items-center space-x-2 text-sm text-gray-600">
                                    <span class="font-medium">v{{ release.version }}</span>
                                    <span class="text-gray-400">•</span>
                                    <span>{{ release.release_id }}</span>
                                </div>
                            </div>
                            
                            <!-- Status Badge -->
                            <div class="ml-4">
                                {% if release.status == 'PLANNING' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Planning
                                    </span>
                                {% elif release.status == 'DEVELOPMENT' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Development
                                    </span>
                                {% elif release.status == 'TESTING' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Testing
                                    </span>
                                {% elif release.status == 'STAGING' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        Staging
                                    </span>
                                {% elif release.status == 'PENDING_APPROVAL' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        Pending Approval
                                    </span>
                                {% elif release.status == 'APPROVED' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Approved
                                    </span>
                                {% elif release.status == 'DEPLOYED' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                                        Deployed
                                    </span>
                                {% elif release.status == 'CANCELLED' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Cancelled
                                    </span>
                                {% elif release.status == 'FAILED' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Failed
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Priority and Type -->
                        <div class="flex items-center space-x-4 mt-3">
                            <div class="flex items-center space-x-1">
                                {% if release.priority == 'CRITICAL' %}
                                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                    <span class="text-xs text-red-600 font-medium">Critical</span>
                                {% elif release.priority == 'HIGH' %}
                                    <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                                    <span class="text-xs text-orange-600 font-medium">High</span>
                                {% elif release.priority == 'MEDIUM' %}
                                    <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                    <span class="text-xs text-yellow-600 font-medium">Medium</span>
                                {% else %}
                                    <div class="w-2 h-2 bg-gray-500 rounded-full"></div>
                                    <span class="text-xs text-gray-600 font-medium">Low</span>
                                {% endif %}
                            </div>
                            <span class="text-xs text-gray-500">{{ release.get_release_type_display }}</span>
                        </div>
                    </div>
                    
                    <!-- Release Content -->
                    <div class="p-6">
                        <p class="text-sm text-gray-600 mb-4 line-clamp-3">{{ release.description|truncatewords:20 }}</p>
                        
                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex items-center justify-between text-xs text-gray-600 mb-1">
                                <span>Progress</span>
                                <span>{{ release.progress_percentage }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                     style="width: {{ release.progress_percentage }}%"></div>
                            </div>
                        </div>
                        
                        <!-- Release Date -->
                        <div class="flex items-center justify-between text-sm">
                            <div class="flex items-center space-x-1 text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <span>{{ release.planned_release_date|date:"M d, Y" }}</span>
                            </div>
                            {% if release.is_overdue %}
                                <span class="text-red-600 font-medium text-xs">Overdue</span>
                            {% elif release.days_until_release is not None %}
                                {% if release.days_until_release == 0 %}
                                    <span class="text-orange-600 font-medium text-xs">Today</span>
                                {% elif release.days_until_release == 1 %}
                                    <span class="text-orange-600 font-medium text-xs">Tomorrow</span>
                                {% elif release.days_until_release <= 7 %}
                                    <span class="text-yellow-600 font-medium text-xs">{{ release.days_until_release }} days</span>
                                {% else %}
                                    <span class="text-gray-600 text-xs">{{ release.days_until_release }} days</span>
                                {% endif %}
                            {% endif %}
                        </div>
                        
                        <!-- Assigned To -->
                        {% if release.assigned_to %}
                            <div class="flex items-center space-x-1 text-xs text-gray-600 mt-2">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>{{ release.assigned_to.get_full_name|default:release.assigned_to.username }}</span>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Actions -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                        <div class="flex items-center justify-between">
                            <a href="{% url 'supply:release_detail' release.id %}" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200">
                                View Details
                            </a>
                            
                            <div class="flex items-center space-x-2">
                                {% if release.can_approve and release.status == 'PENDING_APPROVAL' %}
                                    <a href="{% url 'supply:release_approve' release.id %}" 
                                       class="inline-flex items-center px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 text-xs font-medium rounded-lg transition-colors duration-200">
                                        Approve
                                    </a>
                                {% elif release.can_deploy and release.status == 'APPROVED' %}
                                    <a href="{% url 'supply:release_deploy' release.id %}" 
                                       class="inline-flex items-center px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 text-xs font-medium rounded-lg transition-colors duration-200">
                                        Deploy
                                    </a>
                                {% endif %}
                                
                                {% if release.status not in 'DEPLOYED,CANCELLED,FAILED' %}
                                    <a href="{% url 'supply:release_edit' release.id %}" 
                                       class="inline-flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs font-medium rounded-lg transition-colors duration-200">
                                        Edit
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-lg">
                <div class="flex flex-1 justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                           hx-get="{% url 'supply:releases_dashboard' %}?page={{ page_obj.previous_page_number }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                           hx-target="#releases-list"
                           class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                           hx-get="{% url 'supply:releases_dashboard' %}?page={{ page_obj.next_page_number }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                           hx-target="#releases-list"
                           class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of
                            <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                   hx-get="{% url 'supply:releases_dashboard' %}?page={{ page_obj.previous_page_number }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                   hx-target="#releases-list"
                                   class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                    <span class="sr-only">Previous</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span class="relative z-10 inline-flex items-center bg-blue-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">{{ num }}</span>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <a href="?page={{ num }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                       hx-get="{% url 'supply:releases_dashboard' %}?page={{ num }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                       hx-target="#releases-list"
                                       class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                   hx-get="{% url 'supply:releases_dashboard' %}?page={{ page_obj.next_page_number }}&status={{ status_filter }}&release_type={{ release_type_filter }}&priority={{ priority_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                   hx-target="#releases-list"
                                   class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                    <span class="sr-only">Next</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        {% endif %}
    {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No releases found</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by creating a new release.</p>
            <div class="mt-6">
                <a href="{% url 'supply:release_create' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    New Release
                </a>
            </div>
        </div>
    {% endif %}
</div>
