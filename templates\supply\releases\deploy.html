{% extends 'base_new.html' %}

{% block title %}Deploy Release - {{ release.name }} v{{ release.version }} - MSRRMS{% endblock %}

{% block page_title %}Deploy Release{% endblock %}
{% block mobile_title %}Deploy{% endblock %}

{% block content %}
<div x-data="releaseDeployment()" class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-purple-600 to-purple-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold">Deploy Release</h1>
                <p class="text-purple-100 mt-1">{{ release.name }} v{{ release.version }}</p>
            </div>
            <a href="{% url 'supply:release_detail' release.id %}" 
               class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Release
            </a>
        </div>
    </div>

    <!-- Pre-deployment Checklist -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Pre-deployment Checklist</h3>
        
        <div class="space-y-3">
            <div class="flex items-center">
                <input type="checkbox" x-model="checklist.approved" 
                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <label class="ml-3 text-sm text-gray-700">
                    Release has been approved
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 ml-2">
                        ✓ Approved
                    </span>
                </label>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" x-model="checklist.testing" 
                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <label class="ml-3 text-sm text-gray-700">All testing has been completed successfully</label>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" x-model="checklist.backup" 
                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <label class="ml-3 text-sm text-gray-700">System backup has been created</label>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" x-model="checklist.rollback" 
                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <label class="ml-3 text-sm text-gray-700">Rollback plan is ready and tested</label>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" x-model="checklist.stakeholders" 
                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <label class="ml-3 text-sm text-gray-700">Stakeholders have been notified</label>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" x-model="checklist.maintenance" 
                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <label class="ml-3 text-sm text-gray-700">Maintenance window has been scheduled (if required)</label>
            </div>
        </div>
        
        <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-center">
                <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p class="ml-3 text-sm text-blue-700">
                    Checklist completion: <span x-text="checklistProgress"></span>%
                </p>
            </div>
            <div class="mt-2 w-full bg-blue-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                     :style="`width: ${checklistProgress}%`"></div>
            </div>
        </div>
    </div>

    <!-- Release Information -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Release Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Release Type</dt>
                        <dd class="text-sm text-gray-900">{{ release.get_release_type_display }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Priority</dt>
                        <dd class="text-sm text-gray-900">{{ release.get_priority_display }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Planned Release Date</dt>
                        <dd class="text-sm text-gray-900">{{ release.planned_release_date|date:"M d, Y g:i A" }}</dd>
                    </div>
                </dl>
            </div>
            
            <div>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Approved By</dt>
                        <dd class="text-sm text-gray-900">{{ release.approved_by.get_full_name|default:release.approved_by.username }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Approved At</dt>
                        <dd class="text-sm text-gray-900">{{ release.approved_at|date:"M d, Y g:i A" }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Current Status</dt>
                        <dd class="text-sm text-gray-900">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ release.get_status_display }}
                            </span>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        
        <!-- Deployment Notes -->
        {% if release.deployment_notes %}
            <div class="mt-6">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Deployment Instructions</h4>
                <div class="bg-gray-50 rounded-lg p-3">
                    <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ release.deployment_notes }}</p>
                </div>
            </div>
        {% endif %}
        
        <!-- Rollback Plan -->
        {% if release.rollback_plan %}
            <div class="mt-6">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Rollback Plan</h4>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ release.rollback_plan }}</p>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Deployment Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <form hx-post="{% url 'supply:release_deploy' release.id %}" 
              hx-target="#form-container" 
              hx-swap="outerHTML"
              hx-indicator="#submit-loading"
              @submit="isSubmitting = true"
              class="space-y-6">
            {% csrf_token %}
            
            <div id="form-container" class="p-6 space-y-6">
                <h3 class="text-lg font-semibold text-gray-900">Deployment Confirmation</h3>
                
                <!-- Deployment Notes -->
                <div>
                    <label for="{{ form.deployment_notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Deployment Notes
                    </label>
                    {{ form.deployment_notes }}
                    {% if form.deployment_notes.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.deployment_notes.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500">Add any notes about the deployment process</p>
                </div>
                
                <!-- Confirmation Checkbox -->
                <div class="flex items-start">
                    {{ form.confirm_deployment }}
                    <div class="ml-3">
                        <label for="{{ form.confirm_deployment.id_for_label }}" class="text-sm font-medium text-gray-700">
                            I confirm that I want to deploy this release *
                        </label>
                        <p class="text-xs text-gray-500 mt-1">
                            This action will mark the release as deployed and cannot be undone.
                        </p>
                    </div>
                </div>
                {% if form.confirm_deployment.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.confirm_deployment.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                <div class="flex items-center justify-between">
                    <a href="{% url 'supply:release_detail' release.id %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            :disabled="isSubmitting || checklistProgress < 100"
                            class="inline-flex items-center px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                        </svg>
                        <span x-show="!isSubmitting">Deploy Release</span>
                        <span x-show="isSubmitting" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Deploying...
                        </span>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Warning Notice -->
    <div class="bg-red-50 border border-red-200 rounded-xl p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Critical Warning</h3>
                <div class="mt-2 text-sm text-red-700">
                    <p>Deployment is a critical action that cannot be undone. Ensure all pre-deployment checks are completed and you have verified the rollback plan is ready.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function releaseDeployment() {
    return {
        isSubmitting: false,
        checklist: {
            approved: true, // Already approved
            testing: false,
            backup: false,
            rollback: false,
            stakeholders: false,
            maintenance: false
        },
        
        get checklistProgress() {
            const total = Object.keys(this.checklist).length;
            const completed = Object.values(this.checklist).filter(Boolean).length;
            return Math.round((completed / total) * 100);
        },
        
        init() {
            // Any initialization logic
        }
    }
}
</script>
{% endblock %}
