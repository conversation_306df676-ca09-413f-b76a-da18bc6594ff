from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from supply.models import Release, UserProfile


class ReleaseModelTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        # Update the automatically created profile
        self.user_profile = UserProfile.objects.get(user=self.user)
        self.user_profile.role = 'DEPARTMENT'  # Use valid choice from ROLE_CHOICES
        self.user_profile.department = 'IT'
        self.user_profile.save()

        # Refresh from database to ensure changes are saved
        self.user.refresh_from_db()

        self.gso_user = User.objects.create_user(
            username='gsouser',
            email='<EMAIL>',
            password='gsopass123'
        )
        # Update the automatically created profile
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'IT'
        self.gso_profile.save()

        # Refresh from database to ensure changes are saved
        self.gso_user.refresh_from_db()

    def test_release_creation(self):
        """Test creating a release"""
        release = Release.objects.create(
            name='Test Release',
            version='1.0.0',
            release_type='MINOR',
            status='PLANNING',
            priority='MEDIUM',
            planned_release_date=timezone.now() + timedelta(days=30),
            description='Test release description',
            created_by=self.user
        )
        
        self.assertEqual(release.name, 'Test Release')
        self.assertEqual(release.version, '1.0.0')
        self.assertEqual(release.status, 'PLANNING')
        self.assertTrue(release.release_id.startswith('REL-'))
        self.assertEqual(release.progress_percentage, 10)  # Planning status = 10%

    def test_release_approval(self):
        """Test release approval functionality"""
        release = Release.objects.create(
            name='Test Release',
            version='1.0.0',
            release_type='MINOR',
            status='PENDING_APPROVAL',
            priority='MEDIUM',
            planned_release_date=timezone.now() + timedelta(days=30),
            description='Test release description',
            created_by=self.user
        )
        
        # Test approval permissions
        # Debug: Check user profile
        self.assertEqual(self.gso_user.userprofile.role, 'GSO')
        self.assertEqual(release.status, 'PENDING_APPROVAL')
        self.assertTrue(release.can_approve(self.gso_user))
        self.assertFalse(release.can_approve(self.user))  # Regular user can't approve
        
        # Test approval process
        result = release.approve(self.gso_user)
        self.assertTrue(result)
        self.assertEqual(release.status, 'APPROVED')
        self.assertEqual(release.approved_by, self.gso_user)
        self.assertIsNotNone(release.approved_at)

    def test_release_deployment(self):
        """Test release deployment functionality"""
        release = Release.objects.create(
            name='Test Release',
            version='1.0.0',
            release_type='MINOR',
            status='APPROVED',
            priority='MEDIUM',
            planned_release_date=timezone.now() + timedelta(days=30),
            description='Test release description',
            created_by=self.user,
            approved_by=self.gso_user,
            approved_at=timezone.now()
        )
        
        # Test deployment permissions
        self.assertTrue(release.can_deploy(self.gso_user))
        
        # Test deployment process
        result = release.deploy(self.gso_user, 'Deployment completed successfully')
        self.assertTrue(result)
        self.assertEqual(release.status, 'DEPLOYED')
        self.assertEqual(release.deployed_by, self.gso_user)
        self.assertIsNotNone(release.actual_release_date)
        self.assertEqual(release.progress_percentage, 100)

    def test_overdue_release(self):
        """Test overdue release detection"""
        # Create a release with past planned date
        release = Release.objects.create(
            name='Overdue Release',
            version='1.0.0',
            release_type='MINOR',
            status='DEVELOPMENT',
            priority='HIGH',
            planned_release_date=timezone.now() - timedelta(days=5),
            description='Overdue release',
            created_by=self.user
        )
        
        self.assertTrue(release.is_overdue)
        # Allow for timezone differences - should be around -5 or -6
        self.assertLessEqual(release.days_until_release, -4)

    def test_days_until_release(self):
        """Test days until release calculation"""
        # Create a release with future planned date
        release = Release.objects.create(
            name='Future Release',
            version='1.0.0',
            release_type='MINOR',
            status='DEVELOPMENT',
            priority='MEDIUM',
            planned_release_date=timezone.now() + timedelta(days=10),
            description='Future release',
            created_by=self.user
        )
        
        self.assertFalse(release.is_overdue)
        # Allow for timezone differences - should be around 9 or 10
        self.assertGreaterEqual(release.days_until_release, 9)


class ReleaseViewsTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        # Update the automatically created profile
        self.user_profile = UserProfile.objects.get(user=self.user)
        self.user_profile.role = 'GSO'
        self.user_profile.department = 'IT'
        self.user_profile.save()
        
        self.release = Release.objects.create(
            name='Test Release',
            version='1.0.0',
            release_type='MINOR',
            status='PLANNING',
            priority='MEDIUM',
            planned_release_date=timezone.now() + timedelta(days=30),
            description='Test release description',
            created_by=self.user
        )

    def test_releases_dashboard_view(self):
        """Test releases dashboard view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('supply:releases_dashboard'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Release Management')
        self.assertContains(response, 'Test Release')

    def test_release_detail_view(self):
        """Test release detail view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('supply:release_detail', args=[self.release.id]))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Release')
        self.assertContains(response, '1.0.0')

    def test_release_create_view(self):
        """Test release creation view"""
        self.client.login(username='testuser', password='testpass123')
        
        # GET request
        response = self.client.get(reverse('supply:release_create'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create New Release')
        
        # POST request
        data = {
            'name': 'New Test Release',
            'version': '2.0.0',
            'release_type': 'MAJOR',
            'priority': 'HIGH',
            'planned_release_date': (timezone.now() + timedelta(days=60)).strftime('%Y-%m-%dT%H:%M'),
            'description': 'New test release description',
            'estimated_effort_hours': 40
        }
        response = self.client.post(reverse('supply:release_create'), data)
        
        # Should redirect to release detail page
        self.assertEqual(response.status_code, 302)
        
        # Check if release was created
        new_release = Release.objects.get(name='New Test Release')
        self.assertEqual(new_release.version, '2.0.0')
        self.assertEqual(new_release.created_by, self.user)

    def test_release_edit_view(self):
        """Test release edit view"""
        self.client.login(username='testuser', password='testpass123')
        
        # GET request
        response = self.client.get(reverse('supply:release_edit', args=[self.release.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Edit Release')
        
        # POST request
        data = {
            'name': 'Updated Test Release',
            'version': '1.1.0',
            'release_type': 'MINOR',
            'priority': 'HIGH',
            'planned_release_date': (timezone.now() + timedelta(days=45)).strftime('%Y-%m-%dT%H:%M'),
            'description': 'Updated test release description',
            'estimated_effort_hours': 50
        }
        response = self.client.post(reverse('supply:release_edit', args=[self.release.id]), data)
        
        # Should redirect to release detail page
        self.assertEqual(response.status_code, 302)
        
        # Check if release was updated
        updated_release = Release.objects.get(id=self.release.id)
        self.assertEqual(updated_release.name, 'Updated Test Release')
        self.assertEqual(updated_release.version, '1.1.0')

    def test_release_timeline_view(self):
        """Test release timeline view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('supply:release_timeline'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Release Timeline')
        self.assertContains(response, 'Test Release')

    def test_release_calendar_view(self):
        """Test release calendar view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('supply:release_calendar'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Release Calendar')

    def test_unauthorized_access(self):
        """Test unauthorized access to release management"""
        # Test without login
        response = self.client.get(reverse('supply:releases_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test with non-GSO user
        regular_user = User.objects.create_user(
            username='regular',
            email='<EMAIL>',
            password='regularpass123'
        )
        # Update the automatically created profile
        regular_profile = UserProfile.objects.get(user=regular_user)
        regular_profile.role = 'REQUESTER'
        regular_profile.department = 'HR'
        regular_profile.save()
        
        self.client.login(username='regular', password='regularpass123')
        response = self.client.get(reverse('supply:releases_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to dashboard due to insufficient permissions


class ReleaseFormsTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        # Update the automatically created profile
        self.user_profile = UserProfile.objects.get(user=self.user)
        self.user_profile.role = 'GSO'
        self.user_profile.department = 'IT'
        self.user_profile.save()

    def test_release_form_validation(self):
        """Test release form validation"""
        from supply.forms import ReleaseForm
        
        # Test valid form
        form_data = {
            'name': 'Test Release',
            'version': '1.0.0',
            'release_type': 'MINOR',
            'priority': 'MEDIUM',
            'planned_release_date': timezone.now() + timedelta(days=30),
            'description': 'Test description',
            'assigned_to': self.user.id
        }
        form = ReleaseForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        # Test invalid version format
        form_data['version'] = 'invalid-version'
        form = ReleaseForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('version', form.errors)

    def test_release_filter_form(self):
        """Test release filter form"""
        from supply.forms import ReleaseFilterForm
        
        form_data = {
            'status': 'PLANNING',
            'release_type': 'MINOR',
            'priority': 'HIGH',
            'search': 'test'
        }
        form = ReleaseFilterForm(data=form_data)
        self.assertTrue(form.is_valid())
