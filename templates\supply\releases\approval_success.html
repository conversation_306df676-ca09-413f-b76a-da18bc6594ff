<!-- Success message for HTMX response -->
<div class="bg-green-50 border border-green-200 rounded-xl p-6">
    <div class="flex items-center">
        <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <div class="ml-4">
            <h3 class="text-lg font-semibold text-green-800">
                {% if action == 'approve' %}
                    Release Approved Successfully!
                {% elif action == 'reject' %}
                    Release Rejected
                {% elif action == 'request_changes' %}
                    Changes Requested
                {% endif %}
            </h3>
            <p class="text-green-700 mt-1">
                {% if action == 'approve' %}
                    {{ release.name }} v{{ release.version }} has been approved and is ready for deployment.
                {% elif action == 'reject' %}
                    {{ release.name }} v{{ release.version }} has been rejected and cancelled.
                {% elif action == 'request_changes' %}
                    {{ release.name }} v{{ release.version }} has been sent back for modifications.
                {% endif %}
            </p>
            {% if remarks %}
                <div class="mt-3 p-3 bg-white border border-green-200 rounded-lg">
                    <p class="text-sm text-gray-700"><strong>Remarks:</strong> {{ remarks }}</p>
                </div>
            {% endif %}
            <div class="mt-4 flex space-x-3">
                <a href="{% url 'supply:release_detail' release.id %}" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    View Release
                </a>
                <a href="{% url 'supply:releases_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-redirect after 3 seconds
setTimeout(() => {
    window.location.href = "{% url 'supply:release_detail' release.id %}";
}, 3000);
</script>
