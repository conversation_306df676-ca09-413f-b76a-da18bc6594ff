{% extends 'base_new.html' %}

{% block title %}Create Release - MSRRMS{% endblock %}

{% block page_title %}Create New Release{% endblock %}
{% block mobile_title %}New Release{% endblock %}

{% block content %}
<div x-data="releaseForm()" class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold">Create New Release</h1>
                <p class="text-blue-100 mt-1">Define a new software release with all necessary details</p>
            </div>
            <a href="{% url 'supply:releases_dashboard' %}" 
               class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Releases
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <form hx-post="{% url 'supply:release_create' %}" 
              hx-target="#form-container" 
              hx-swap="outerHTML"
              hx-indicator="#submit-loading"
              @submit="isSubmitting = true"
              class="space-y-6">
            {% csrf_token %}
            
            <div id="form-container" class="p-6 space-y-6">
                <!-- Basic Information -->
                <div class="border-b border-gray-200 pb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Release Name *
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div>
                            <label for="{{ form.version.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Version *
                            </label>
                            {{ form.version }}
                            {% if form.version.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.version.errors.0 }}</p>
                            {% endif %}
                            <p class="mt-1 text-xs text-gray-500">Format: X.Y.Z (e.g., 1.2.3)</p>
                        </div>
                        
                        <div>
                            <label for="{{ form.release_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Release Type *
                            </label>
                            {{ form.release_type }}
                            {% if form.release_type.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.release_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div>
                            <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Priority *
                            </label>
                            {{ form.priority }}
                            {% if form.priority.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.priority.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="{{ form.planned_release_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Planned Release Date *
                            </label>
                            {{ form.planned_release_date }}
                            {% if form.planned_release_date.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.planned_release_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="{{ form.assigned_to.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Assigned To
                            </label>
                            {{ form.assigned_to }}
                            {% if form.assigned_to.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.assigned_to.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Description and Documentation -->
                <div class="border-b border-gray-200 pb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Description & Documentation</h3>
                    <div class="space-y-6">
                        <div>
                            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Description *
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="{{ form.release_notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    Release Notes
                                </label>
                                {{ form.release_notes }}
                                {% if form.release_notes.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.release_notes.errors.0 }}</p>
                                {% endif %}
                                <p class="mt-1 text-xs text-gray-500">User-facing release notes</p>
                            </div>
                            
                            <div>
                                <label for="{{ form.changelog.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    Technical Changelog
                                </label>
                                {{ form.changelog }}
                                {% if form.changelog.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.changelog.errors.0 }}</p>
                                {% endif %}
                                <p class="mt-1 text-xs text-gray-500">Technical changes and updates</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Deployment Information -->
                <div class="border-b border-gray-200 pb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Deployment Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="{{ form.deployment_notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Deployment Notes
                            </label>
                            {{ form.deployment_notes }}
                            {% if form.deployment_notes.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.deployment_notes.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div>
                            <label for="{{ form.rollback_plan.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Rollback Plan
                            </label>
                            {{ form.rollback_plan }}
                            {% if form.rollback_plan.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.rollback_plan.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Dependencies and Systems -->
                <div class="border-b border-gray-200 pb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Dependencies & Systems</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="{{ form.dependencies.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Dependencies
                            </label>
                            {{ form.dependencies }}
                            {% if form.dependencies.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.dependencies.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div>
                            <label for="{{ form.affected_systems.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Affected Systems
                            </label>
                            {{ form.affected_systems }}
                            {% if form.affected_systems.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.affected_systems.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Effort Estimation -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Effort Estimation</h3>
                    <div class="max-w-xs">
                        <label for="{{ form.estimated_effort_hours.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Estimated Effort (Hours)
                        </label>
                        {{ form.estimated_effort_hours }}
                        {% if form.estimated_effort_hours.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.estimated_effort_hours.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                <div class="flex items-center justify-between">
                    <a href="{% url 'supply:releases_dashboard' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            :disabled="isSubmitting"
                            class="inline-flex items-center px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <span x-show="!isSubmitting">Create Release</span>
                        <span x-show="isSubmitting" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Creating...
                        </span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
function releaseForm() {
    return {
        isSubmitting: false,
        
        init() {
            // Set minimum date to today
            const today = new Date();
            const dateInput = document.querySelector('input[type="datetime-local"]');
            if (dateInput) {
                const minDate = today.toISOString().slice(0, 16);
                dateInput.min = minDate;
            }
        }
    }
}
</script>
{% endblock %}
