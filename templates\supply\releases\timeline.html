{% extends 'base_new.html' %}

{% block title %}Release Timeline - MSRRMS{% endblock %}

{% block page_title %}Release Timeline{% endblock %}
{% block mobile_title %}Timeline{% endblock %}

{% block content %}
<div x-data="releaseTimeline()" class="space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold">Release Timeline</h1>
                <p class="text-blue-100 mt-1">Visual timeline of all releases and their schedules</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'supply:release_calendar' %}" 
                   class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Calendar View
                </a>
                <a href="{% url 'supply:releases_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Filter by Date Range</h3>
            <button @click="showFilters = !showFilters" 
                    class="md:hidden inline-flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors duration-200">
                <span x-text="showFilters ? 'Hide' : 'Show'"></span>
                <svg class="w-4 h-4 ml-1" :class="{'rotate-180': showFilters}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>
        
        <div x-show="showFilters" x-collapse class="md:block">
            <form method="get" class="flex flex-col sm:flex-row gap-4 items-end">
                <div class="flex-1">
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                    <input type="date" name="date_from" id="date_from" value="{{ date_from }}"
                           class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                </div>
                <div class="flex-1">
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                    <input type="date" name="date_to" id="date_to" value="{{ date_to }}"
                           class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                </div>
                <div class="flex space-x-2">
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        Apply Filter
                    </button>
                    <a href="{% url 'supply:release_timeline' %}" 
                       class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Timeline -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        {% if releases %}
            <div class="relative">
                <!-- Timeline Line -->
                <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300"></div>
                
                <!-- Timeline Items -->
                <div class="space-y-8">
                    {% for release in releases %}
                        <div class="relative flex items-start">
                            <!-- Timeline Dot -->
                            <div class="flex-shrink-0 w-16 flex justify-center">
                                <div class="w-4 h-4 rounded-full border-4 border-white shadow-lg
                                    {% if release.status == 'DEPLOYED' %}
                                        bg-green-500
                                    {% elif release.status == 'APPROVED' %}
                                        bg-blue-500
                                    {% elif release.status == 'PENDING_APPROVAL' %}
                                        bg-orange-500
                                    {% elif release.status == 'CANCELLED' or release.status == 'FAILED' %}
                                        bg-red-500
                                    {% elif release.is_overdue %}
                                        bg-red-400
                                    {% else %}
                                        bg-gray-400
                                    {% endif %}"></div>
                            </div>
                            
                            <!-- Timeline Content -->
                            <div class="flex-1 ml-6">
                                <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors duration-200">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h4 class="text-lg font-semibold text-gray-900">
                                                <a href="{% url 'supply:release_detail' release.id %}" 
                                                   class="hover:text-blue-600 transition-colors duration-200">
                                                    {{ release.name }} v{{ release.version }}
                                                </a>
                                            </h4>
                                            <p class="text-sm text-gray-600 mt-1">{{ release.description|truncatewords:20 }}</p>
                                            
                                            <!-- Release Info -->
                                            <div class="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                    </svg>
                                                    {{ release.planned_release_date|date:"M d, Y" }}
                                                </span>
                                                <span>{{ release.get_release_type_display }}</span>
                                                <span>{{ release.get_priority_display }} Priority</span>
                                                {% if release.assigned_to %}
                                                    <span class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                        </svg>
                                                        {{ release.assigned_to.get_full_name|default:release.assigned_to.username }}
                                                    </span>
                                                {% endif %}
                                            </div>
                                            
                                            <!-- Progress Bar -->
                                            <div class="mt-3">
                                                <div class="flex items-center justify-between text-xs text-gray-600 mb-1">
                                                    <span>Progress</span>
                                                    <span>{{ release.progress_percentage }}%</span>
                                                </div>
                                                <div class="w-full bg-gray-200 rounded-full h-2">
                                                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                                         style="width: {{ release.progress_percentage }}%"></div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Status Badge -->
                                        <div class="ml-4">
                                            {% if release.status == 'PLANNING' %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    Planning
                                                </span>
                                            {% elif release.status == 'DEVELOPMENT' %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    Development
                                                </span>
                                            {% elif release.status == 'TESTING' %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    Testing
                                                </span>
                                            {% elif release.status == 'STAGING' %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                    Staging
                                                </span>
                                            {% elif release.status == 'PENDING_APPROVAL' %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                    Pending Approval
                                                </span>
                                            {% elif release.status == 'APPROVED' %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Approved
                                                </span>
                                            {% elif release.status == 'DEPLOYED' %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                                                    Deployed
                                                </span>
                                            {% elif release.status == 'CANCELLED' %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    Cancelled
                                                </span>
                                            {% elif release.status == 'FAILED' %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    Failed
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <!-- Time Indicator -->
                                    <div class="mt-3 flex items-center justify-between">
                                        <div class="text-xs text-gray-500">
                                            {% if release.is_overdue %}
                                                <span class="text-red-600 font-medium">Overdue</span>
                                            {% elif release.days_until_release is not None %}
                                                {% if release.days_until_release == 0 %}
                                                    <span class="text-orange-600 font-medium">Today</span>
                                                {% elif release.days_until_release == 1 %}
                                                    <span class="text-orange-600 font-medium">Tomorrow</span>
                                                {% elif release.days_until_release <= 7 %}
                                                    <span class="text-yellow-600 font-medium">{{ release.days_until_release }} days</span>
                                                {% else %}
                                                    <span>{{ release.days_until_release }} days</span>
                                                {% endif %}
                                            {% endif %}
                                        </div>
                                        
                                        <div class="flex items-center space-x-2">
                                            <a href="{% url 'supply:release_detail' release.id %}" 
                                               class="text-blue-600 hover:text-blue-800 text-xs font-medium transition-colors duration-200">
                                                View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No releases found</h3>
                <p class="mt-1 text-sm text-gray-500">No releases match the selected date range.</p>
                <div class="mt-6">
                    <a href="{% url 'supply:release_create' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Create New Release
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<script>
function releaseTimeline() {
    return {
        showFilters: true,
        
        init() {
            // Initialize on mobile
            if (window.innerWidth < 768) {
                this.showFilters = false;
            }
        }
    }
}
</script>
{% endblock %}
