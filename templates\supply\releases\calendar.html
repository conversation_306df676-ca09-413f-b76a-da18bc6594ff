{% extends 'base_new.html' %}

{% block title %}Release Calendar - MSRRMS{% endblock %}

{% block page_title %}Release Calendar{% endblock %}
{% block mobile_title %}Calendar{% endblock %}

{% block extra_head %}
<!-- FullCalendar CSS -->
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
<style>
    .fc-event.release-planning { background-color: #6b7280; border-color: #6b7280; }
    .fc-event.release-development { background-color: #3b82f6; border-color: #3b82f6; }
    .fc-event.release-testing { background-color: #f59e0b; border-color: #f59e0b; }
    .fc-event.release-staging { background-color: #8b5cf6; border-color: #8b5cf6; }
    .fc-event.release-pending_approval { background-color: #f97316; border-color: #f97316; }
    .fc-event.release-approved { background-color: #10b981; border-color: #10b981; }
    .fc-event.release-deployed { background-color: #059669; border-color: #059669; }
    .fc-event.release-cancelled { background-color: #ef4444; border-color: #ef4444; }
    .fc-event.release-failed { background-color: #dc2626; border-color: #dc2626; }
    
    .fc-toolbar-title {
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        color: #1f2937 !important;
    }
    
    .fc-button {
        background-color: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
        font-weight: 500 !important;
        border-radius: 0.5rem !important;
        padding: 0.5rem 1rem !important;
    }
    
    .fc-button:hover {
        background-color: #2563eb !important;
        border-color: #2563eb !important;
    }
    
    .fc-button:disabled {
        background-color: #9ca3af !important;
        border-color: #9ca3af !important;
    }
    
    .fc-daygrid-day-number {
        color: #374151 !important;
        font-weight: 500 !important;
    }
    
    .fc-col-header-cell {
        background-color: #f9fafb !important;
        font-weight: 600 !important;
        color: #374151 !important;
    }
    
    .fc-today {
        background-color: #dbeafe !important;
    }
</style>
{% endblock %}

{% block content %}
<div x-data="releaseCalendar()" class="space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold">Release Calendar</h1>
                <p class="text-blue-100 mt-1">Calendar view of all scheduled releases</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'supply:release_timeline' %}" 
                   class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    Timeline View
                </a>
                <a href="{% url 'supply:releases_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Legend -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Status Legend</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-gray-500 rounded"></div>
                <span class="text-sm text-gray-700">Planning</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-blue-500 rounded"></div>
                <span class="text-sm text-gray-700">Development</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-yellow-500 rounded"></div>
                <span class="text-sm text-gray-700">Testing</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-purple-500 rounded"></div>
                <span class="text-sm text-gray-700">Staging</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-orange-500 rounded"></div>
                <span class="text-sm text-gray-700">Pending Approval</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-green-500 rounded"></div>
                <span class="text-sm text-gray-700">Approved</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-emerald-600 rounded"></div>
                <span class="text-sm text-gray-700">Deployed</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-red-500 rounded"></div>
                <span class="text-sm text-gray-700">Cancelled/Failed</span>
            </div>
        </div>
    </div>

    <!-- Calendar -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div id="calendar"></div>
    </div>

    <!-- Release Details Modal -->
    <div x-show="showModal" 
         x-cloak
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click.away="showModal = false">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Modal Header -->
                <div class="flex items-start justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900" x-text="selectedRelease.title"></h3>
                        <p class="text-sm text-gray-600" x-text="selectedRelease.date"></p>
                    </div>
                    <button @click="showModal = false" 
                            class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <!-- Modal Content -->
                <div class="space-y-4">
                    <div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              :class="getStatusBadgeClass(selectedRelease.status)"
                              x-text="selectedRelease.status"></span>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-1">Type & Priority</h4>
                        <p class="text-sm text-gray-700">
                            <span x-text="selectedRelease.type"></span> • 
                            <span x-text="selectedRelease.priority"></span> Priority
                        </p>
                    </div>
                    
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <button @click="showModal = false" 
                                class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                            Close
                        </button>
                        <a :href="selectedRelease.url" 
                           class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- FullCalendar JS -->
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>

<script>
function releaseCalendar() {
    return {
        showModal: false,
        selectedRelease: {},
        calendar: null,
        
        init() {
            this.initializeCalendar();
        },
        
        initializeCalendar() {
            const calendarEl = document.getElementById('calendar');
            const events = {{ calendar_events|safe }};
            
            this.calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,listWeek'
                },
                events: events,
                eventClick: (info) => {
                    this.showReleaseDetails(info.event);
                },
                eventMouseEnter: (info) => {
                    info.el.style.cursor = 'pointer';
                },
                height: 'auto',
                aspectRatio: 1.8,
                eventDisplay: 'block',
                dayMaxEvents: 3,
                moreLinkClick: 'popover',
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                }
            });
            
            this.calendar.render();
        },
        
        showReleaseDetails(event) {
            this.selectedRelease = {
                title: event.title,
                date: event.start.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                }),
                status: event.extendedProps.status,
                type: event.extendedProps.type,
                priority: event.extendedProps.priority,
                url: event.url
            };
            this.showModal = true;
        },
        
        getStatusBadgeClass(status) {
            const classes = {
                'PLANNING': 'bg-gray-100 text-gray-800',
                'DEVELOPMENT': 'bg-blue-100 text-blue-800',
                'TESTING': 'bg-yellow-100 text-yellow-800',
                'STAGING': 'bg-purple-100 text-purple-800',
                'PENDING_APPROVAL': 'bg-orange-100 text-orange-800',
                'APPROVED': 'bg-green-100 text-green-800',
                'DEPLOYED': 'bg-emerald-100 text-emerald-800',
                'CANCELLED': 'bg-red-100 text-red-800',
                'FAILED': 'bg-red-100 text-red-800'
            };
            return classes[status] || 'bg-gray-100 text-gray-800';
        }
    }
}
</script>
{% endblock %}
