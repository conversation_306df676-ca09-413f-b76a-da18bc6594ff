from django.contrib import admin
from .models import UserProfile, SupplyItem, SupplyRequest, InventoryTransaction, Release


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'department', 'created_at']
    list_filter = ['role', 'department']
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'department']


@admin.register(SupplyItem)
class SupplyItemAdmin(admin.ModelAdmin):
    list_display = ['name', 'current_stock', 'minimum_stock', 'unit', 'is_low_stock', 'updated_at']
    list_filter = ['unit', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.boolean = True
    is_low_stock.short_description = 'Low Stock'


@admin.register(SupplyRequest)
class SupplyRequestAdmin(admin.ModelAdmin):
    list_display = ['request_id', 'requester', 'department', 'item', 'quantity', 'status', 'created_at']
    list_filter = ['status', 'department', 'created_at']
    search_fields = ['request_id', 'requester__username', 'item__name', 'department']
    readonly_fields = ['request_id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Request Information', {
            'fields': ('request_id', 'requester', 'department', 'item', 'quantity', 'purpose', 'status')
        }),
        ('Approval Information', {
            'fields': ('approved_by', 'approved_at', 'approval_remarks'),
            'classes': ('collapse',)
        }),
        ('Release Information', {
            'fields': ('released_by', 'released_at', 'release_remarks'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(InventoryTransaction)
class InventoryTransactionAdmin(admin.ModelAdmin):
    list_display = ['item', 'transaction_type', 'quantity', 'performed_by', 'created_at']
    list_filter = ['transaction_type', 'created_at']
    search_fields = ['item__name', 'performed_by__username', 'remarks']
    readonly_fields = ['created_at']


@admin.register(Release)
class ReleaseAdmin(admin.ModelAdmin):
    list_display = ['release_id', 'name', 'version', 'release_type', 'status', 'priority', 'planned_release_date', 'created_by']
    list_filter = ['status', 'release_type', 'priority', 'planned_release_date', 'created_at']
    search_fields = ['release_id', 'name', 'version', 'description']
    readonly_fields = ['release_id', 'created_at', 'updated_at', 'approved_at', 'actual_release_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('release_id', 'name', 'version', 'release_type', 'status', 'priority')
        }),
        ('Timeline', {
            'fields': ('planned_release_date', 'actual_release_date', 'created_at', 'updated_at')
        }),
        ('Content', {
            'fields': ('description', 'release_notes', 'changelog', 'deployment_notes', 'rollback_plan')
        }),
        ('People & Approval', {
            'fields': ('created_by', 'assigned_to', 'approved_by', 'approved_at', 'deployed_by')
        }),
        ('Dependencies & Systems', {
            'fields': ('dependencies', 'affected_systems'),
            'classes': ('collapse',)
        }),
        ('Metrics', {
            'fields': ('estimated_effort_hours', 'actual_effort_hours'),
            'classes': ('collapse',)
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = list(self.readonly_fields)
        if obj and obj.status == 'DEPLOYED':
            # Make most fields readonly for deployed releases
            readonly_fields.extend(['name', 'version', 'release_type', 'planned_release_date', 'description'])
        return readonly_fields
