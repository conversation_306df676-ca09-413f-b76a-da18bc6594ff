{% extends 'base_new.html' %}

{% block title %}Supply Release Management - MSRRMS{% endblock %}

{% block page_title %}Supply Release Management{% endblock %}
{% block mobile_title %}Supply Releases{% endblock %}

{% block content %}
<div x-data="supplyReleaseManagement()" class="space-y-6">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-green-600 to-green-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold">Supply Release Management</h1>
                <p class="text-green-100 mt-1">Manage the release of approved supply requests to departments</p>
            </div>
            <div class="hidden md:flex items-center space-x-6">
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ total_approved }}</div>
                    <div class="text-green-200 text-sm">Total Approved</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-200">{{ available_count }}</div>
                    <div class="text-green-200 text-sm">Available</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-200">{{ partial_count }}</div>
                    <div class="text-green-200 text-sm">Partial</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-200">{{ insufficient_count }}</div>
                    <div class="text-green-200 text-sm">Insufficient</div>
                </div>
            </div>
        </div>
        
        <!-- Mobile Stats -->
        <div class="md:hidden mt-4 grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-xl font-bold">{{ total_approved }}</div>
                <div class="text-green-200 text-xs">Total</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-yellow-200">{{ available_count }}</div>
                <div class="text-green-200 text-xs">Available</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-orange-200">{{ partial_count }}</div>
                <div class="text-green-200 text-xs">Partial</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-red-200">{{ insufficient_count }}</div>
                <div class="text-green-200 text-xs">Insufficient</div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div class="flex flex-wrap gap-2">
            <a href="{% url 'supply:releases_dashboard' %}"
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Software Releases
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
            <button @click="showFilters = !showFilters" 
                    class="md:hidden inline-flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors duration-200">
                <span x-text="showFilters ? 'Hide' : 'Show'"></span>
                <svg class="w-4 h-4 ml-1" :class="{'rotate-180': showFilters}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>
        
        <div x-show="showFilters" x-collapse class="md:block">
            <form hx-get="{% url 'supply:release_management' %}" 
                  hx-target="#release-list" 
                  hx-trigger="change, keyup delay:500ms from:input[type=text]"
                  class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                    <select name="department" class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 sm:text-sm">
                        <option value="">All Departments</option>
                        {% for dept in departments %}
                            <option value="{{ dept }}" {% if dept == department_filter %}selected{% endif %}>{{ dept }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Stock Status</label>
                    <select name="stock_filter" class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 sm:text-sm">
                        <option value="">All Status</option>
                        <option value="available" {% if stock_filter == 'available' %}selected{% endif %}>Available</option>
                        <option value="partial" {% if stock_filter == 'partial' %}selected{% endif %}>Partial</option>
                        <option value="insufficient" {% if stock_filter == 'insufficient' %}selected{% endif %}>Insufficient</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" name="search" value="{{ search_query }}" 
                           placeholder="Search requests..."
                           class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 sm:text-sm">
                </div>
                
                <div class="flex items-end">
                    <button type="button" @click="clearFilters()" 
                            class="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        Clear Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Supply Requests List -->
    <div id="release-list">
        {% include 'supply/gso/release_list.html' %}
    </div>
</div>

<script>
function supplyReleaseManagement() {
    return {
        showFilters: true,
        
        init() {
            // Initialize on mobile
            if (window.innerWidth < 768) {
                this.showFilters = false;
            }
        },
        
        clearFilters() {
            // Clear all form inputs
            document.querySelectorAll('#release-list form input, #release-list form select').forEach(input => {
                if (input.type === 'text' || input.type === 'search') {
                    input.value = '';
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                }
            });
            
            // Trigger refresh
            htmx.ajax('GET', '{% url "supply:release_management" %}', {
                target: '#release-list',
                swap: 'outerHTML'
            });
        }
    }
}
</script>
{% endblock %}
