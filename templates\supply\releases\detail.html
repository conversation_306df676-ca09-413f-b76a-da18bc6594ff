{% extends 'base_new.html' %}

{% block title %}{{ release.name }} v{{ release.version }} - MSRRMS{% endblock %}

{% block page_title %}{{ release.name }} v{{ release.version }}{% endblock %}
{% block mobile_title %}Release Detail{% endblock %}

{% block content %}
<div x-data="releaseDetail()" class="space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                    <h1 class="text-2xl font-bold">{{ release.name }}</h1>
                    <span class="text-lg text-blue-200">v{{ release.version }}</span>
                    <span class="text-sm text-blue-300">{{ release.release_id }}</span>
                </div>
                
                <div class="flex items-center space-x-4 text-sm text-blue-100">
                    <span>{{ release.get_release_type_display }}</span>
                    <span>•</span>
                    <span>{{ release.get_priority_display }} Priority</span>
                    <span>•</span>
                    <span>Created {{ release.created_at|date:"M d, Y" }}</span>
                </div>
                
                <!-- Status Badge -->
                <div class="mt-3">
                    {% if release.status == 'PLANNING' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                            Planning
                        </span>
                    {% elif release.status == 'DEVELOPMENT' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            Development
                        </span>
                    {% elif release.status == 'TESTING' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            Testing
                        </span>
                    {% elif release.status == 'STAGING' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                            Staging
                        </span>
                    {% elif release.status == 'PENDING_APPROVAL' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                            Pending Approval
                        </span>
                    {% elif release.status == 'APPROVED' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            Approved
                        </span>
                    {% elif release.status == 'DEPLOYED' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-emerald-100 text-emerald-800">
                            Deployed
                        </span>
                    {% elif release.status == 'CANCELLED' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            Cancelled
                        </span>
                    {% elif release.status == 'FAILED' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            Failed
                        </span>
                    {% endif %}
                </div>
            </div>
            
            <!-- Actions -->
            <div class="flex items-center space-x-3">
                {% if can_approve and release.status == 'PENDING_APPROVAL' %}
                    <a href="{% url 'supply:release_approve' release.id %}" 
                       class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Approve
                    </a>
                {% elif can_deploy and release.status == 'APPROVED' %}
                    <a href="{% url 'supply:release_deploy' release.id %}" 
                       class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                        </svg>
                        Deploy
                    </a>
                {% endif %}
                
                {% if release.status not in 'DEPLOYED,CANCELLED,FAILED' %}
                    <a href="{% url 'supply:release_edit' release.id %}" 
                       class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit
                    </a>
                {% endif %}
                
                <a href="{% url 'supply:releases_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Releases
                </a>
            </div>
        </div>
    </div>

    <!-- Progress and Timeline -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Progress & Timeline</h3>
        
        <!-- Progress Bar -->
        <div class="mb-6">
            <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Release Progress</span>
                <span>{{ release.progress_percentage }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
                <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" 
                     style="width: {{ release.progress_percentage }}%"></div>
            </div>
        </div>
        
        <!-- Timeline Info -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">{{ release.planned_release_date|date:"M d" }}</div>
                <div class="text-sm text-gray-600">Planned Release</div>
                {% if release.is_overdue %}
                    <div class="text-xs text-red-600 font-medium mt-1">Overdue</div>
                {% elif release.days_until_release is not None %}
                    {% if release.days_until_release == 0 %}
                        <div class="text-xs text-orange-600 font-medium mt-1">Today</div>
                    {% elif release.days_until_release == 1 %}
                        <div class="text-xs text-orange-600 font-medium mt-1">Tomorrow</div>
                    {% elif release.days_until_release <= 7 %}
                        <div class="text-xs text-yellow-600 font-medium mt-1">{{ release.days_until_release }} days</div>
                    {% else %}
                        <div class="text-xs text-gray-600 mt-1">{{ release.days_until_release }} days</div>
                    {% endif %}
                {% endif %}
            </div>
            
            {% if release.actual_release_date %}
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ release.actual_release_date|date:"M d" }}</div>
                    <div class="text-sm text-gray-600">Actual Release</div>
                </div>
            {% endif %}
            
            {% if release.estimated_effort_hours %}
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ release.estimated_effort_hours }}h</div>
                    <div class="text-sm text-gray-600">Estimated Effort</div>
                    {% if release.actual_effort_hours %}
                        <div class="text-xs text-gray-600 mt-1">Actual: {{ release.actual_effort_hours }}h</div>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Description -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Description</h3>
                <p class="text-gray-700 whitespace-pre-wrap">{{ release.description }}</p>
            </div>
            
            <!-- Release Notes -->
            {% if release.release_notes %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Release Notes</h3>
                    <div class="prose prose-sm max-w-none">
                        <p class="text-gray-700 whitespace-pre-wrap">{{ release.release_notes }}</p>
                    </div>
                </div>
            {% endif %}
            
            <!-- Technical Changelog -->
            {% if release.changelog %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Technical Changelog</h3>
                    <div class="prose prose-sm max-w-none">
                        <p class="text-gray-700 whitespace-pre-wrap">{{ release.changelog }}</p>
                    </div>
                </div>
            {% endif %}
            
            <!-- Deployment Information -->
            {% if release.deployment_notes or release.rollback_plan %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Deployment Information</h3>
                    
                    {% if release.deployment_notes %}
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Deployment Notes</h4>
                            <p class="text-gray-700 whitespace-pre-wrap">{{ release.deployment_notes }}</p>
                        </div>
                    {% endif %}
                    
                    {% if release.rollback_plan %}
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Rollback Plan</h4>
                            <p class="text-gray-700 whitespace-pre-wrap">{{ release.rollback_plan }}</p>
                        </div>
                    {% endif %}
                </div>
            {% endif %}
        </div>
        
        <!-- Right Column -->
        <div class="space-y-6">
            <!-- Release Information -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Release Information</h3>
                
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created By</dt>
                        <dd class="text-sm text-gray-900">{{ release.created_by.get_full_name|default:release.created_by.username }}</dd>
                    </div>
                    
                    {% if release.assigned_to %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Assigned To</dt>
                            <dd class="text-sm text-gray-900">{{ release.assigned_to.get_full_name|default:release.assigned_to.username }}</dd>
                        </div>
                    {% endif %}
                    
                    {% if release.approved_by %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Approved By</dt>
                            <dd class="text-sm text-gray-900">{{ release.approved_by.get_full_name|default:release.approved_by.username }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Approved At</dt>
                            <dd class="text-sm text-gray-900">{{ release.approved_at|date:"M d, Y g:i A" }}</dd>
                        </div>
                    {% endif %}
                    
                    {% if release.deployed_by %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Deployed By</dt>
                            <dd class="text-sm text-gray-900">{{ release.deployed_by.get_full_name|default:release.deployed_by.username }}</dd>
                        </div>
                    {% endif %}
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="text-sm text-gray-900">{{ release.updated_at|date:"M d, Y g:i A" }}</dd>
                    </div>
                </dl>
            </div>
            
            <!-- Dependencies & Systems -->
            {% if release.dependencies or release.affected_systems %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Dependencies & Systems</h3>
                    
                    {% if release.dependencies %}
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Dependencies</h4>
                            <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ release.dependencies }}</p>
                        </div>
                    {% endif %}
                    
                    {% if release.affected_systems %}
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Affected Systems</h4>
                            <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ release.affected_systems }}</p>
                        </div>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function releaseDetail() {
    return {
        init() {
            // Any initialization logic
        }
    }
}
</script>
{% endblock %}
