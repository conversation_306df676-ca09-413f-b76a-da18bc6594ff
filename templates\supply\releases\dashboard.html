{% extends 'base_new.html' %}

{% block title %}Release Management - MSRRMS{% endblock %}

{% block page_title %}Release Management{% endblock %}
{% block mobile_title %}Releases{% endblock %}

{% block extra_head %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div x-data="releaseManagement()" class="space-y-6">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold">Release Management</h1>
                <p class="text-blue-100 mt-1">Manage software releases and deployment schedules</p>
            </div>
            <div class="hidden md:flex items-center space-x-6">
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ total_releases }}</div>
                    <div class="text-blue-200 text-sm">Total Releases</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-200">{{ active_releases }}</div>
                    <div class="text-blue-200 text-sm">Active</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-200">{{ overdue_releases }}</div>
                    <div class="text-blue-200 text-sm">Overdue</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-200">{{ pending_approval }}</div>
                    <div class="text-blue-200 text-sm">Pending Approval</div>
                </div>
            </div>
        </div>
        
        <!-- Mobile Stats -->
        <div class="md:hidden mt-4 grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-xl font-bold">{{ total_releases }}</div>
                <div class="text-blue-200 text-xs">Total</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-yellow-200">{{ active_releases }}</div>
                <div class="text-blue-200 text-xs">Active</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-red-200">{{ overdue_releases }}</div>
                <div class="text-blue-200 text-xs">Overdue</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-green-200">{{ pending_approval }}</div>
                <div class="text-blue-200 text-xs">Pending</div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div class="flex flex-wrap gap-2">
            <a href="{% url 'supply:release_create' %}"
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                New Release
            </a>
            <a href="{% url 'supply:release_timeline' %}"
               class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                Timeline
            </a>
            <a href="{% url 'supply:release_calendar' %}"
               class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Calendar
            </a>
        </div>
        
        <!-- Auto-refresh toggle -->
        <div class="flex items-center space-x-2">
            <label class="flex items-center">
                <input type="checkbox" x-model="autoRefresh" @change="toggleAutoRefresh()" 
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <span class="ml-2 text-sm text-gray-600">Auto-refresh</span>
            </label>
            <span x-show="autoRefresh" class="text-xs text-gray-500" x-text="'Last updated: ' + lastRefresh.toLocaleTimeString()"></span>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
            <button @click="showFilters = !showFilters" 
                    class="md:hidden inline-flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors duration-200">
                <span x-text="showFilters ? 'Hide' : 'Show'"></span>
                <svg class="w-4 h-4 ml-1" :class="{'rotate-180': showFilters}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>
        
        <div x-show="showFilters" x-collapse class="md:block">
            <form hx-get="{% url 'supply:releases_dashboard' %}" 
                  hx-target="#releases-list" 
                  hx-trigger="change, keyup delay:500ms from:input[type=text]"
                  class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                
                {{ filter_form.status }}
                {{ filter_form.release_type }}
                {{ filter_form.priority }}
                {{ filter_form.search }}
                
                <div class="flex items-end">
                    <button type="button" @click="clearFilters()" 
                            class="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        Clear Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Status Distribution Chart -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Release Status Distribution</h3>
            <div class="relative h-64">
                <canvas id="statusChart"></canvas>
            </div>
        </div>

        <!-- Release Timeline Chart -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Releases by Month</h3>
            <div class="relative h-64">
                <canvas id="timelineChart"></canvas>
            </div>
        </div>

        <!-- Priority Distribution -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Priority Distribution</h3>
            <div class="relative h-64">
                <canvas id="priorityChart"></canvas>
            </div>
        </div>

        <!-- Release Type Distribution -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Release Types</h3>
            <div class="relative h-64">
                <canvas id="typeChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Releases List -->
    <div id="releases-list">
        {% include 'supply/releases/releases_list.html' %}
    </div>
</div>

<script>
function releaseManagement() {
    return {
        showFilters: true,
        autoRefresh: false,
        refreshInterval: null,
        lastRefresh: new Date(),
        
        init() {
            // Initialize on mobile
            if (window.innerWidth < 768) {
                this.showFilters = false;
            }

            // Initialize charts
            this.initializeCharts();
        },

        initializeCharts() {
            const chartData = {{ chart_data|safe }};

            // Status Distribution Chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: chartData.status.labels,
                    datasets: [{
                        data: chartData.status.data,
                        backgroundColor: [
                            '#6b7280', '#3b82f6', '#f59e0b', '#8b5cf6',
                            '#f97316', '#10b981', '#059669', '#ef4444'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });

            // Timeline Chart
            const timelineCtx = document.getElementById('timelineChart').getContext('2d');
            new Chart(timelineCtx, {
                type: 'line',
                data: {
                    labels: chartData.timeline.labels,
                    datasets: [{
                        label: 'Releases',
                        data: chartData.timeline.data,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#f3f4f6'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Priority Chart
            const priorityCtx = document.getElementById('priorityChart').getContext('2d');
            new Chart(priorityCtx, {
                type: 'bar',
                data: {
                    labels: chartData.priority.labels,
                    datasets: [{
                        data: chartData.priority.data,
                        backgroundColor: ['#6b7280', '#f59e0b', '#f97316', '#ef4444'],
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#f3f4f6'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Type Chart
            const typeCtx = document.getElementById('typeChart').getContext('2d');
            new Chart(typeCtx, {
                type: 'pie',
                data: {
                    labels: chartData.type.labels,
                    datasets: [{
                        data: chartData.type.data,
                        backgroundColor: ['#ef4444', '#3b82f6', '#10b981', '#f59e0b'],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        },
        
        toggleAutoRefresh() {
            if (this.autoRefresh) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        },
        
        startAutoRefresh() {
            this.refreshInterval = setInterval(() => {
                if (!document.hidden) {
                    htmx.ajax('GET', '{% url "supply:releases_dashboard" %}', {
                        target: '#releases-list',
                        swap: 'outerHTML'
                    });
                    this.lastRefresh = new Date();
                }
            }, 30000); // Refresh every 30 seconds
        },
        
        stopAutoRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        },
        
        clearFilters() {
            // Clear all form inputs
            document.querySelectorAll('#releases-list form input, #releases-list form select').forEach(input => {
                if (input.type === 'text' || input.type === 'search') {
                    input.value = '';
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                }
            });
            
            // Trigger refresh
            htmx.ajax('GET', '{% url "supply:releases_dashboard" %}', {
                target: '#releases-list',
                swap: 'outerHTML'
            });
        }
    }
}
</script>
{% endblock %}
