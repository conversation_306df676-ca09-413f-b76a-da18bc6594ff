# Generated by Django 4.2.17 on 2025-07-23 02:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('supply', '0004_auditlog'),
    ]

    operations = [
        migrations.CreateModel(
            name='Release',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('release_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('name', models.CharField(help_text='Release name or title', max_length=200)),
                ('version', models.CharField(help_text='Version number (e.g., 1.2.3)', max_length=50)),
                ('release_type', models.CharField(choices=[('MAJOR', 'Major Release'), ('MINOR', 'Minor Release'), ('PATCH', 'Patch Release'), ('HOTFIX', 'Hotfix Release')], default='MINOR', max_length=10)),
                ('status', models.CharField(choices=[('PLANNING', 'Planning'), ('DEVELOPMENT', 'Development'), ('TESTING', 'Testing'), ('STAGING', 'Staging'), ('PENDING_APPROVAL', 'Pending Approval'), ('APPROVED', 'Approved'), ('DEPLOYED', 'Deployed'), ('CANCELLED', 'Cancelled'), ('FAILED', 'Failed')], default='PLANNING', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], default='MEDIUM', max_length=10)),
                ('planned_release_date', models.DateTimeField(help_text='Planned release date')),
                ('actual_release_date', models.DateTimeField(blank=True, help_text='Actual release date', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('description', models.TextField(help_text='Release description')),
                ('release_notes', models.TextField(blank=True, help_text='Release notes for end users')),
                ('changelog', models.TextField(blank=True, help_text='Technical changelog')),
                ('deployment_notes', models.TextField(blank=True, help_text='Deployment instructions and notes')),
                ('rollback_plan', models.TextField(blank=True, help_text='Rollback plan in case of issues')),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('dependencies', models.TextField(blank=True, help_text='Dependencies and requirements (JSON format)')),
                ('affected_systems', models.TextField(blank=True, help_text='Systems affected by this release')),
                ('estimated_effort_hours', models.PositiveIntegerField(blank=True, help_text='Estimated effort in hours', null=True)),
                ('actual_effort_hours', models.PositiveIntegerField(blank=True, help_text='Actual effort in hours', null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_releases', to=settings.AUTH_USER_MODEL)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_releases', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_releases', to=settings.AUTH_USER_MODEL)),
                ('deployed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deployed_releases', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Release',
                'verbose_name_plural': 'Releases',
                'ordering': ['-planned_release_date', '-created_at'],
                'indexes': [models.Index(fields=['status', 'planned_release_date'], name='supply_rele_status_bbb612_idx'), models.Index(fields=['release_type', 'status'], name='supply_rele_release_04db5a_idx'), models.Index(fields=['created_by', 'status'], name='supply_rele_created_4984b8_idx'), models.Index(fields=['priority', 'planned_release_date'], name='supply_rele_priorit_cccece_idx')],
            },
        ),
    ]
