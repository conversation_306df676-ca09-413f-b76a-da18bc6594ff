{% extends 'base_new.html' %}

{% block title %}Approve Release - {{ release.name }} v{{ release.version }} - MSRRMS{% endblock %}

{% block page_title %}Approve Release{% endblock %}
{% block mobile_title %}Approve{% endblock %}

{% block content %}
<div x-data="releaseApproval()" class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-orange-600 to-orange-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold">Approve Release</h1>
                <p class="text-orange-100 mt-1">{{ release.name }} v{{ release.version }}</p>
            </div>
            <a href="{% url 'supply:release_detail' release.id %}" 
               class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Release
            </a>
        </div>
    </div>

    <!-- Release Summary -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Release Summary</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Release Type</dt>
                        <dd class="text-sm text-gray-900">{{ release.get_release_type_display }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Priority</dt>
                        <dd class="text-sm text-gray-900">{{ release.get_priority_display }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Planned Release Date</dt>
                        <dd class="text-sm text-gray-900">{{ release.planned_release_date|date:"M d, Y g:i A" }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created By</dt>
                        <dd class="text-sm text-gray-900">{{ release.created_by.get_full_name|default:release.created_by.username }}</dd>
                    </div>
                </dl>
            </div>
            
            <div>
                <dl class="space-y-3">
                    {% if release.assigned_to %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Assigned To</dt>
                            <dd class="text-sm text-gray-900">{{ release.assigned_to.get_full_name|default:release.assigned_to.username }}</dd>
                        </div>
                    {% endif %}
                    {% if release.estimated_effort_hours %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Estimated Effort</dt>
                            <dd class="text-sm text-gray-900">{{ release.estimated_effort_hours }} hours</dd>
                        </div>
                    {% endif %}
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Current Status</dt>
                        <dd class="text-sm text-gray-900">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                {{ release.get_status_display }}
                            </span>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        
        <!-- Description -->
        <div class="mt-6">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Description</h4>
            <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ release.description }}</p>
        </div>
        
        <!-- Progress -->
        <div class="mt-6">
            <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Release Progress</span>
                <span>{{ release.progress_percentage }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
                <div class="bg-orange-600 h-3 rounded-full transition-all duration-300" 
                     style="width: {{ release.progress_percentage }}%"></div>
            </div>
        </div>
    </div>

    <!-- Approval Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <form hx-post="{% url 'supply:release_approve' release.id %}" 
              hx-target="#form-container" 
              hx-swap="outerHTML"
              hx-indicator="#submit-loading"
              @submit="isSubmitting = true"
              class="space-y-6">
            {% csrf_token %}
            
            <div id="form-container" class="p-6 space-y-6">
                <h3 class="text-lg font-semibold text-gray-900">Approval Decision</h3>
                
                <!-- Action Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Choose Action *</label>
                    <div class="space-y-3">
                        {% for choice in form.action %}
                            <div class="flex items-start">
                                {{ choice.tag }}
                                <div class="ml-3">
                                    <label for="{{ choice.id_for_label }}" class="text-sm font-medium text-gray-700">
                                        {% if choice.choice_value == 'approve' %}
                                            <span class="text-green-700">Approve Release</span>
                                            <p class="text-xs text-gray-500 mt-1">Approve this release for deployment</p>
                                        {% elif choice.choice_value == 'reject' %}
                                            <span class="text-red-700">Reject Release</span>
                                            <p class="text-xs text-gray-500 mt-1">Reject and cancel this release</p>
                                        {% elif choice.choice_value == 'request_changes' %}
                                            <span class="text-yellow-700">Request Changes</span>
                                            <p class="text-xs text-gray-500 mt-1">Send back for modifications</p>
                                        {% endif %}
                                    </label>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    {% if form.action.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.action.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Remarks -->
                <div>
                    <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Remarks
                    </label>
                    {{ form.remarks }}
                    {% if form.remarks.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.remarks.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500">Add any comments or feedback about this decision</p>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                <div class="flex items-center justify-between">
                    <a href="{% url 'supply:release_detail' release.id %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            :disabled="isSubmitting"
                            class="inline-flex items-center px-6 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        <span x-show="!isSubmitting">Submit Decision</span>
                        <span x-show="isSubmitting" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                        </span>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Warning Notice -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Important Notice</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <p>This action cannot be undone. Please review all release details carefully before making your decision.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function releaseApproval() {
    return {
        isSubmitting: false,
        
        init() {
            // Any initialization logic
        }
    }
}
</script>
{% endblock %}
